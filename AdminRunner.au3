#include <ButtonConstants.au3>
#include <EditConstants.au3>
#include <GUIConstantsEx.au3>
#include <StaticConstants.au3>
#include <WindowsConstants.au3>
#include <FileConstants.au3>
#include <MsgBoxConstants.au3>

; 创建GUI窗体
$hGUI = GUICreate("管理员权限运行程序", 500, 300, -1, -1)

; 创建控件
$lblTitle = GUICtrlCreateLabel("选择要以管理员权限运行的程序", 20, 20, 460, 20, $SS_CENTER)
GUICtrlSetFont($lblTitle, 12, 600)

$lblFile = GUICtrlCreateLabel("程序路径:", 20, 60, 80, 20)
$txtFilePath = GUICtrlCreateInput("", 100, 58, 300, 22, $ES_READONLY)
$btnBrowse = GUICtrlCreateButton("浏览...", 410, 57, 70, 25)

$lblRunPath = GUICtrlCreateLabel("运行路径:", 20, 100, 80, 20)
$txtRunPath = GUICtrlCreateInput("C:\", 100, 98, 300, 22)
$lblRunPathTip = GUICtrlCreateLabel("(程序启动的工作目录)", 410, 100, 120, 20)
GUICtrlSetColor($lblRunPathTip, 0x808080)

$lblParams = GUICtrlCreateLabel("运行参数:", 20, 140, 80, 20)
$txtParams = GUICtrlCreateInput("", 100, 138, 300, 22)
$lblParamsTip = GUICtrlCreateLabel("(可选，程序的命令行参数)", 410, 140, 150, 20)
GUICtrlSetColor($lblParamsTip, 0x808080)

$lblDomain = GUICtrlCreateLabel("域名/机器:", 20, 180, 80, 20)
$txtDomain = GUICtrlCreateInput("", 100, 178, 300, 22)
$lblDomainTip = GUICtrlCreateLabel("(留空表示本机)", 410, 180, 100, 20)
GUICtrlSetColor($lblDomainTip, 0x808080)

; 创建按钮
$btnRun = GUICtrlCreateButton("以管理员权限运行", 150, 220, 120, 35)
GUICtrlSetFont($btnRun, 10, 600)
$btnExit = GUICtrlCreateButton("退出", 290, 220, 80, 35)

; 创建状态栏
$lblStatus = GUICtrlCreateLabel("请选择要运行的程序文件", 20, 270, 460, 20)
GUICtrlSetColor($lblStatus, 0x0000FF)

; 显示GUI
GUISetState(@SW_SHOW, $hGUI)

; 主循环
While 1
    $nMsg = GUIGetMsg()
    Switch $nMsg
        Case $GUI_EVENT_CLOSE, $btnExit
            Exit
            
        Case $btnBrowse
            ; 浏览文件对话框
            $sFilePath = FileOpenDialog("选择要运行的程序", @ProgramFilesDir, "可执行文件 (*.exe)|所有文件 (*.*)", $FD_FILEMUSTEXIST)
            If Not @error Then
                GUICtrlSetData($txtFilePath, $sFilePath)
                ; 自动设置运行路径为程序所在目录
                $sDir = StringLeft($sFilePath, StringInStr($sFilePath, "\", 0, -1))
                GUICtrlSetData($txtRunPath, $sDir)
                GUICtrlSetData($lblStatus, "已选择文件: " & StringRight($sFilePath, 50))
            EndIf
            
        Case $btnRun
            ; 获取输入的值
            $sFilePath = GUICtrlRead($txtFilePath)
            $sRunPath = GUICtrlRead($txtRunPath)
            $sParams = GUICtrlRead($txtParams)
            $sDomain = GUICtrlRead($txtDomain)
            
            ; 验证输入
            If $sFilePath = "" Then
                MsgBox($MB_ICONWARNING, "警告", "请先选择要运行的程序文件！")
                ContinueLoop
            EndIf
            
            If Not FileExists($sFilePath) Then
                MsgBox($MB_ICONERROR, "错误", "选择的文件不存在！")
                ContinueLoop
            EndIf
            
            If $sRunPath = "" Then
                $sRunPath = "C:\"
            EndIf
            
            ; 构建命令
            $sCommand = $sFilePath
            If $sParams <> "" Then
                $sCommand = '"' & $sFilePath & ' ' & $sParams & '"'
            EndIf
            
            ; 构建lsrunase命令
            $sLsrunaseCmd = @ScriptDir & "\提权程序\lsrunase.exe"
            $sCmdLine = '/user:administrator /password:40lkg/kN/6Pm3ffhFw== /domain:' & $sDomain & ' /command:' & $sCommand & ' /runpath:' & $sRunPath
            
            ; 检查lsrunase.exe是否存在
            If Not FileExists($sLsrunaseCmd) Then
                MsgBox($MB_ICONERROR, "错误", "找不到lsrunase.exe文件！" & @CRLF & "请确保提权程序文件夹存在于脚本目录下。")
                ContinueLoop
            EndIf
            
            GUICtrlSetData($lblStatus, "正在以管理员权限运行程序...")
            
            ; 执行命令
            $iPID = Run($sLsrunaseCmd & " " & $sCmdLine, @ScriptDir, @SW_HIDE)
            
            If $iPID > 0 Then
                GUICtrlSetData($lblStatus, "程序已启动 (PID: " & $iPID & ")")
                ; 可选：询问是否关闭当前程序
                $iResult = MsgBox($MB_YESNO + $MB_ICONQUESTION, "提示", "程序已成功启动！" & @CRLF & "是否关闭当前管理工具？")
                If $iResult = $IDYES Then
                    Exit
                EndIf
            Else
                MsgBox($MB_ICONERROR, "错误", "启动程序失败！" & @CRLF & "请检查权限设置或联系管理员。")
                GUICtrlSetData($lblStatus, "启动失败")
            EndIf
    EndSwitch
WEnd
