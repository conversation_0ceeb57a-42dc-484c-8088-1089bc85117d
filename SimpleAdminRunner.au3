#include <ButtonConstants.au3>
#include <EditConstants.au3>
#include <GUIConstantsEx.au3>
#include <StaticConstants.au3>
#include <WindowsConstants.au3>
#include <FileConstants.au3>
#include <MsgBoxConstants.au3>

; 程序信息
$sTitle = "管理员权限运行工具"
$sVersion = "v1.0"

; 创建主窗体
$hGUI = GUICreate($sTitle & " " & $sVersion, 450, 200, -1, -1)
GUISetIcon(@SystemDir & "\shell32.dll", 21) ; 设置图标

; 创建控件
$lblTitle = GUICtrlCreateLabel("选择程序文件，以管理员权限运行", 20, 15, 410, 20, $SS_CENTER)
GUICtrlSetFont($lblTitle, 11, 600)
GUICtrlSetColor($lblTitle, 0x000080)

; 文件选择区域
$grpFile = GUICtrlCreateGroup("程序选择", 20, 45, 410, 60)
$txtFilePath = GUICtrlCreateInput("", 35, 65, 300, 22, $ES_READONLY)
$btnBrowse = GUICtrlCreateButton("浏览", 345, 64, 70, 24)
GUICtrlCreateGroup("", -99, -99, 1, 1) ; 关闭组

; 操作按钮
$btnRun = GUICtrlCreateButton("以管理员权限运行", 120, 125, 130, 35)
GUICtrlSetFont($btnRun, 10, 600)
GUICtrlSetColor($btnRun, 0xFFFFFF)
GUICtrlSetBkColor($btnRun, 0x008000)

$btnExit = GUICtrlCreateButton("退出", 270, 125, 80, 35)

; 状态信息
$lblStatus = GUICtrlCreateLabel("请选择要运行的程序文件", 20, 175, 410, 20, $SS_CENTER)
GUICtrlSetColor($lblStatus, 0x808080)

; 显示窗体
GUISetState(@SW_SHOW, $hGUI)

; 主事件循环
While 1
    $nMsg = GUIGetMsg()
    Switch $nMsg
        Case $GUI_EVENT_CLOSE, $btnExit
            Exit
            
        Case $btnBrowse
            ; 打开文件选择对话框
            $sSelectedFile = FileOpenDialog("选择要运行的程序", @ProgramFilesDir, "可执行文件 (*.exe)", $FD_FILEMUSTEXIST)
            If Not @error Then
                GUICtrlSetData($txtFilePath, $sSelectedFile)
                $sFileName = StringRight($sSelectedFile, StringLen($sSelectedFile) - StringInStr($sSelectedFile, "\", 0, -1))
                GUICtrlSetData($lblStatus, "已选择: " & $sFileName)
            EndIf
            
        Case $btnRun
            RunAsAdmin()
    EndSwitch
WEnd

; 以管理员权限运行程序的函数
Func RunAsAdmin()
    ; 获取选择的文件路径
    $sFilePath = GUICtrlRead($txtFilePath)
    
    ; 验证文件是否选择
    If $sFilePath = "" Then
        MsgBox($MB_ICONWARNING, "提示", "请先选择要运行的程序文件！")
        Return
    EndIf
    
    ; 验证文件是否存在
    If Not FileExists($sFilePath) Then
        MsgBox($MB_ICONERROR, "错误", "选择的文件不存在或已被删除！")
        Return
    EndIf
    
    ; 获取程序所在目录作为运行路径
    $sRunPath = StringLeft($sFilePath, StringInStr($sFilePath, "\", 0, -1))
    
    ; lsrunase.exe的路径
    $sLsrunasePath = @ScriptDir & "\提权程序\lsrunase.exe"
    
    ; 检查lsrunase.exe是否存在
    If Not FileExists($sLsrunasePath) Then
        MsgBox($MB_ICONERROR, "错误", "找不到lsrunase.exe！" & @CRLF & "请确保提权程序文件夹在脚本同目录下。")
        Return
    EndIf
    
    ; 构建命令参数
    $sCmdParams = '/user:administrator /password:40lkg/kN/6Pm3ffhFw== /domain: /command:"' & $sFilePath & '" /runpath:"' & $sRunPath & '"'
    
    ; 更新状态
    GUICtrlSetData($lblStatus, "正在启动程序...")
    
    ; 执行命令
    $iPID = Run('"' & $sLsrunasePath & '" ' & $sCmdParams, @ScriptDir, @SW_HIDE)
    
    If $iPID > 0 Then
        GUICtrlSetData($lblStatus, "程序已成功启动！")
        
        ; 询问是否关闭工具
        $iChoice = MsgBox($MB_YESNO + $MB_ICONQUESTION, "成功", "程序已以管理员权限启动！" & @CRLF & @CRLF & "是否关闭此工具？")
        If $iChoice = $IDYES Then
            Exit
        Else
            GUICtrlSetData($lblStatus, "可以继续选择其他程序运行")
        EndIf
    Else
        MsgBox($MB_ICONERROR, "失败", "启动程序失败！" & @CRLF & "可能的原因：" & @CRLF & "1. 密码不正确" & @CRLF & "2. 权限不足" & @CRLF & "3. 目标程序有问题")
        GUICtrlSetData($lblStatus, "启动失败，请重试")
    EndIf
EndFunc
