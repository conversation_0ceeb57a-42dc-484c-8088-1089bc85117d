#include <ButtonConstants.au3>
#include <EditConstants.au3>
#include <GUIConstantsEx.au3>
#include <StaticConstants.au3>
#include <WindowsConstants.au3>
#include <FileConstants.au3>
#include <MsgBoxConstants.au3>

; 创建主窗体
$hGUI = GUICreate("直接生成EXE封装工具", 480, 280, -1, -1)

; 创建控件
$lblTitle = GUICtrlCreateLabel("直接生成包含提权功能的EXE文件", 20, 15, 440, 20, $SS_CENTER)
GUICtrlSetFont($lblTitle, 12, 600)
GUICtrlSetColor($lblTitle, 0x000080)

; 目标程序选择
$lblTarget = GUICtrlCreateLabel("选择要封装的程序:", 20, 50, 120, 20)
$txtTargetPath = GUICtrlCreateInput("", 20, 70, 350, 22, $ES_READONLY)
$btnBrowse = GUICtrlCreateButton("浏览", 380, 69, 70, 24)

; 输出设置
$lblOutput = GUICtrlCreateLabel("输出EXE文件名:", 20, 110, 100, 20)
$txtOutputName = GUICtrlCreateInput("", 20, 130, 280, 22)
$btnOutputDir = GUICtrlCreateButton("选择目录", 310, 129, 70, 24)
$btnDesktop = GUICtrlCreateButton("桌面", 390, 129, 60, 24)

; AutoIt编译器路径
$lblCompiler = GUICtrlCreateLabel("AutoIt编译器路径:", 20, 170, 120, 20)
$txtCompilerPath = GUICtrlCreateInput("", 20, 190, 350, 22, $ES_READONLY)
$btnFindCompiler = GUICtrlCreateButton("查找", 380, 189, 70, 24)

; 操作按钮
$btnGenerate = GUICtrlCreateButton("生成EXE文件", 150, 230, 100, 35)
GUICtrlSetFont($btnGenerate, 10, 600)
GUICtrlSetBkColor($btnGenerate, 0x008000)
GUICtrlSetColor($btnGenerate, 0xFFFFFF)
$btnExit = GUICtrlCreateButton("退出", 270, 230, 70, 35)

; 状态信息
$lblStatus = GUICtrlCreateLabel("请选择要封装的程序文件", 20, 275, 440, 20)

; 全局变量
Global $sOutputDir = @DesktopDir

; 显示窗体
GUISetState(@SW_SHOW, $hGUI)

; 自动查找AutoIt编译器
AutoFindCompiler()

; 主事件循环
While 1
    $nMsg = GUIGetMsg()
    Switch $nMsg
        Case $GUI_EVENT_CLOSE, $btnExit
            Exit
            
        Case $btnBrowse
            $sFile = FileOpenDialog("选择要封装的程序", @ProgramFilesDir, "可执行文件 (*.exe)", $FD_FILEMUSTEXIST)
            If Not @error Then
                GUICtrlSetData($txtTargetPath, $sFile)
                ; 自动生成输出文件名
                $sBaseName = StringTrimRight(StringRight($sFile, StringLen($sFile) - StringInStr($sFile, "\", 0, -1)), 4)
                GUICtrlSetData($txtOutputName, $sBaseName & "_Admin.exe")
                GUICtrlSetData($lblStatus, "已选择: " & StringRight($sFile, 50))
            EndIf
            
        Case $btnOutputDir
            $sDir = FileSelectFolder("选择输出目录", $sOutputDir)
            If Not @error Then
                $sOutputDir = $sDir
                GUICtrlSetData($lblStatus, "输出目录: " & $sOutputDir)
            EndIf
            
        Case $btnDesktop
            $sOutputDir = @DesktopDir
            GUICtrlSetData($lblStatus, "输出目录: 桌面")
            
        Case $btnFindCompiler
            $sCompiler = FileOpenDialog("选择AutoIt编译器", @ProgramFilesDir, "AutoIt编译器 (Aut2exe.exe)", $FD_FILEMUSTEXIST)
            If Not @error Then
                GUICtrlSetData($txtCompilerPath, $sCompiler)
            EndIf
            
        Case $btnGenerate
            GenerateEXE()
    EndSwitch
WEnd

; 自动查找AutoIt编译器
Func AutoFindCompiler()
    ; 常见的AutoIt安装路径
    Local $aPaths[] = [ _
        @ProgramFilesDir & "\AutoIt3\Aut2exe\Aut2exe.exe", _
        @ProgramFilesDir & " (x86)\AutoIt3\Aut2exe\Aut2exe.exe", _
        "C:\Program Files\AutoIt3\Aut2exe\Aut2exe.exe", _
        "C:\Program Files (x86)\AutoIt3\Aut2exe\Aut2exe.exe" _
    ]
    
    For $i = 0 To UBound($aPaths) - 1
        If FileExists($aPaths[$i]) Then
            GUICtrlSetData($txtCompilerPath, $aPaths[$i])
            Return
        EndIf
    Next
    
    ; 如果没找到，提示用户
    GUICtrlSetData($lblStatus, "未找到AutoIt编译器，请手动选择")
EndFunc

; 生成EXE文件
Func GenerateEXE()
    $sTargetPath = GUICtrlRead($txtTargetPath)
    $sOutputName = GUICtrlRead($txtOutputName)
    $sCompilerPath = GUICtrlRead($txtCompilerPath)
    
    ; 验证输入
    If $sTargetPath = "" Then
        MsgBox(48, "提示", "请选择要封装的程序！")
        Return
    EndIf
    
    If $sOutputName = "" Then
        MsgBox(48, "提示", "请输入输出文件名！")
        Return
    EndIf
    
    If $sCompilerPath = "" Or Not FileExists($sCompilerPath) Then
        MsgBox(48, "提示", "请选择有效的AutoIt编译器路径！")
        Return
    EndIf
    
    If Not FileExists($sTargetPath) Then
        MsgBox(16, "错误", "目标程序不存在！")
        Return
    EndIf
    
    $sLsrunasePath = @ScriptDir & "\提权程序\lsrunase.exe"
    If Not FileExists($sLsrunasePath) Then
        MsgBox(16, "错误", "找不到lsrunase.exe！")
        Return
    EndIf
    
    ; 确保输出文件名有.exe扩展名
    If Not StringRight($sOutputName, 4) = ".exe" Then
        $sOutputName &= ".exe"
    EndIf
    
    GUICtrlSetData($lblStatus, "正在生成封装脚本...")
    
    ; 生成临时脚本
    $sTempScript = @TempDir & "\PackageTemp_" & Random(1000, 9999, 1) & ".au3"
    $sScript = CreatePackageScript($sTargetPath, $sLsrunasePath)
    
    $hFile = FileOpen($sTempScript, 2)
    If $hFile = -1 Then
        MsgBox(16, "错误", "无法创建临时脚本！")
        Return
    EndIf
    FileWrite($hFile, $sScript)
    FileClose($hFile)
    
    GUICtrlSetData($lblStatus, "正在编译EXE文件...")
    
    ; 编译脚本为EXE
    $sOutputPath = $sOutputDir & "\" & $sOutputName
    $sCmdLine = '"' & $sCompilerPath & '" /in "' & $sTempScript & '" /out "' & $sOutputPath & '"'
    
    $iPID = Run($sCmdLine, @TempDir, @SW_HIDE, $STDOUT_CHILD)
    ProcessWaitClose($iPID)
    
    ; 清理临时文件
    FileDelete($sTempScript)
    
    ; 检查是否成功生成
    If FileExists($sOutputPath) Then
        GUICtrlSetData($lblStatus, "EXE文件生成成功！")
        MsgBox(64, "成功", "EXE文件已生成！" & @CRLF & @CRLF & "位置: " & $sOutputPath & @CRLF & @CRLF & "您现在可以直接运行这个EXE文件，它会以管理员权限启动目标程序。")
    Else
        MsgBox(16, "失败", "EXE文件生成失败！" & @CRLF & "请检查AutoIt编译器路径是否正确。")
        GUICtrlSetData($lblStatus, "生成失败")
    EndIf
EndFunc

; 创建封装脚本内容
Func CreatePackageScript($sTargetPath, $sLsrunasePath)
    ; 读取文件为Base64
    $sTargetBase64 = FileToBase64($sTargetPath)
    $sLsrunaseBase64 = FileToBase64($sLsrunasePath)
    $sTargetFileName = StringRight($sTargetPath, StringLen($sTargetPath) - StringInStr($sTargetPath, "\", 0, -1))
    
    $sScript = '; 自动生成的管理员权限运行程序' & @CRLF
    $sScript &= '; 目标程序: ' & $sTargetFileName & @CRLF & @CRLF
    
    $sScript &= '#NoTrayIcon' & @CRLF
    $sScript &= '#include <FileConstants.au3>' & @CRLF & @CRLF
    
    $sScript &= '; 嵌入的文件数据' & @CRLF
    $sScript &= 'Global $sTargetData = "' & $sTargetBase64 & '"' & @CRLF
    $sScript &= 'Global $sLsrunaseData = "' & $sLsrunaseBase64 & '"' & @CRLF
    $sScript &= 'Global $sTargetFileName = "' & $sTargetFileName & '"' & @CRLF & @CRLF
    
    $sScript &= '; 主程序' & @CRLF
    $sScript &= 'Main()' & @CRLF & @CRLF
    
    $sScript &= 'Func Main()' & @CRLF
    $sScript &= '    $sTempDir = @TempDir & "\AR_" & Random(1000, 9999, 1)' & @CRLF
    $sScript &= '    DirCreate($sTempDir)' & @CRLF
    $sScript &= '    $sTargetPath = $sTempDir & "\" & $sTargetFileName' & @CRLF
    $sScript &= '    $sLsrunasePath = $sTempDir & "\lsrunase.exe"' & @CRLF
    $sScript &= '    FileWrite($sTargetPath, _Base64Decode($sTargetData))' & @CRLF
    $sScript &= '    FileWrite($sLsrunasePath, _Base64Decode($sLsrunaseData))' & @CRLF
    $sScript &= '    $sCmdLine = ''/user:administrator /password:40lkg/kN/6Pm3ffhFw== /domain: /command:"'' & $sTargetPath & ''" /runpath:"'' & $sTempDir & ''"''' & @CRLF
    $sScript &= '    Run(''"'' & $sLsrunasePath & ''" '' & $sCmdLine, $sTempDir, @SW_HIDE)' & @CRLF
    $sScript &= '    Sleep(2000)' & @CRLF
    $sScript &= '    FileDelete($sTargetPath)' & @CRLF
    $sScript &= '    FileDelete($sLsrunasePath)' & @CRLF
    $sScript &= '    DirRemove($sTempDir)' & @CRLF
    $sScript &= 'EndFunc' & @CRLF & @CRLF
    
    $sScript &= 'Func _Base64Decode($sData)' & @CRLF
    $sScript &= '    $aCall = DllCall("crypt32.dll", "bool", "CryptStringToBinaryW", "wstr", $sData, "dword", 0, "dword", 1, "ptr", 0, "dword*", 0, "ptr", 0, "ptr", 0)' & @CRLF
    $sScript &= '    $bBuffer = DllStructCreate("byte[" & $aCall[5] & "]")' & @CRLF
    $sScript &= '    DllCall("crypt32.dll", "bool", "CryptStringToBinaryW", "wstr", $sData, "dword", 0, "dword", 1, "ptr", DllStructGetPtr($bBuffer), "dword*", $aCall[5], "ptr", 0, "ptr", 0)' & @CRLF
    $sScript &= '    Return DllStructGetData($bBuffer, 1)' & @CRLF
    $sScript &= 'EndFunc'
    
    Return $sScript
EndFunc

; 文件转Base64
Func FileToBase64($sFilePath)
    $hFile = FileOpen($sFilePath, 16)
    $bData = FileRead($hFile)
    FileClose($hFile)
    
    $aCall = DllCall("crypt32.dll", "bool", "CryptBinaryToStringW", "ptr", $bData, "dword", BinaryLen($bData), "dword", 1, "ptr", 0, "dword*", 0)
    $sBase64 = DllStructCreate("wchar[" & $aCall[5] & "]")
    DllCall("crypt32.dll", "bool", "CryptBinaryToStringW", "ptr", $bData, "dword", BinaryLen($bData), "dword", 1, "ptr", DllStructGetPtr($sBase64), "dword*", $aCall[5])
    
    Return StringReplace(DllStructGetData($sBase64, 1), @CRLF, "")
EndFunc
