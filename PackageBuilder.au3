#include <ButtonConstants.au3>
#include <EditConstants.au3>
#include <GUIConstantsEx.au3>
#include <StaticConstants.au3>
#include <WindowsConstants.au3>
#include <FileConstants.au3>
#include <MsgBoxConstants.au3>
#include <Array.au3>

; 程序信息
$sTitle = "EXE封装工具"
$sVersion = "v1.0"

; 创建主窗体
$hGUI = GUICreate($sTitle & " " & $sVersion, 500, 350, -1, -1)

; 创建控件
$lblTitle = GUICtrlCreateLabel("将目标程序与提权工具封装为单一EXE文件", 20, 15, 460, 20, $SS_CENTER)
GUICtrlSetFont($lblTitle, 11, 600)
GUICtrlSetColor($lblTitle, 0x000080)

; 目标程序选择
$grpTarget = GUICtrlCreateGroup("目标程序", 20, 45, 460, 60)
$txtTargetPath = GUICtrlCreateInput("", 35, 65, 350, 22, $ES_READONLY)
$btnBrowseTarget = GUICtrlCreateButton("浏览", 395, 64, 70, 24)
GUICtrlCreateGroup("", -99, -99, 1, 1)

; 输出设置
$grpOutput = GUICtrlCreateGroup("输出设置", 20, 115, 460, 100)
$lblOutputName = GUICtrlCreateLabel("输出文件名:", 35, 140, 80, 20)
$txtOutputName = GUICtrlCreateInput("", 120, 138, 250, 22)
$lblOutputPath = GUICtrlCreateLabel("输出目录:", 35, 170, 80, 20)
$txtOutputPath = GUICtrlCreateInput(@DesktopDir, 120, 168, 250, 22, $ES_READONLY)
$btnBrowseOutput = GUICtrlCreateButton("浏览", 380, 167, 70, 24)
$chkAutoName = GUICtrlCreateCheckbox("自动生成文件名", 120, 195, 120, 20)
GUICtrlSetState($chkAutoName, $GUI_CHECKED)
GUICtrlCreateGroup("", -99, -99, 1, 1)

; 操作按钮
$btnPackage = GUICtrlCreateButton("开始封装", 150, 240, 100, 35)
GUICtrlSetFont($btnPackage, 10, 600)
$btnExit = GUICtrlCreateButton("退出", 270, 240, 80, 35)

; 状态信息
$lblStatus = GUICtrlCreateLabel("请选择要封装的目标程序", 20, 290, 460, 40, $SS_LEFT)
GUICtrlSetColor($lblStatus, 0x808080)

; 显示窗体
GUISetState(@SW_SHOW, $hGUI)

; 主事件循环
While 1
    $nMsg = GUIGetMsg()
    Switch $nMsg
        Case $GUI_EVENT_CLOSE, $btnExit
            Exit
            
        Case $btnBrowseTarget
            $sTargetFile = FileOpenDialog("选择要封装的程序", @ProgramFilesDir, "可执行文件 (*.exe)", $FD_FILEMUSTEXIST)
            If Not @error Then
                GUICtrlSetData($txtTargetPath, $sTargetFile)
                ; 自动生成输出文件名
                If GUICtrlRead($chkAutoName) = $GUI_CHECKED Then
                    $sBaseName = StringTrimRight(StringRight($sTargetFile, StringLen($sTargetFile) - StringInStr($sTargetFile, "\", 0, -1)), 4)
                    GUICtrlSetData($txtOutputName, $sBaseName & "_Admin.exe")
                EndIf
                GUICtrlSetData($lblStatus, "已选择目标程序: " & StringRight($sTargetFile, 50))
            EndIf
            
        Case $btnBrowseOutput
            $sOutputDir = FileSelectFolder("选择输出目录", @DesktopDir)
            If Not @error Then
                GUICtrlSetData($txtOutputPath, $sOutputDir)
            EndIf
            
        Case $chkAutoName
            If GUICtrlRead($chkAutoName) = $GUI_CHECKED And GUICtrlRead($txtTargetPath) <> "" Then
                $sTargetFile = GUICtrlRead($txtTargetPath)
                $sBaseName = StringTrimRight(StringRight($sTargetFile, StringLen($sTargetFile) - StringInStr($sTargetFile, "\", 0, -1)), 4)
                GUICtrlSetData($txtOutputName, $sBaseName & "_Admin.exe")
            EndIf
            
        Case $btnPackage
            CreatePackage()
    EndSwitch
WEnd

; 创建封装程序的函数
Func CreatePackage()
    ; 获取输入值
    $sTargetPath = GUICtrlRead($txtTargetPath)
    $sOutputName = GUICtrlRead($txtOutputName)
    $sOutputPath = GUICtrlRead($txtOutputPath)
    
    ; 验证输入
    If $sTargetPath = "" Then
        MsgBox($MB_ICONWARNING, "提示", "请选择要封装的目标程序！")
        Return
    EndIf
    
    If $sOutputName = "" Then
        MsgBox($MB_ICONWARNING, "提示", "请输入输出文件名！")
        Return
    EndIf
    
    If Not FileExists($sTargetPath) Then
        MsgBox($MB_ICONERROR, "错误", "目标程序文件不存在！")
        Return
    EndIf
    
    $sLsrunasePath = @ScriptDir & "\提权程序\lsrunase.exe"
    If Not FileExists($sLsrunasePath) Then
        MsgBox($MB_ICONERROR, "错误", "找不到lsrunase.exe文件！")
        Return
    EndIf
    
    ; 确保输出文件名有.exe扩展名
    If Not StringRight($sOutputName, 4) = ".exe" Then
        $sOutputName &= ".exe"
    EndIf
    
    $sFullOutputPath = $sOutputPath & "\" & $sOutputName
    
    GUICtrlSetData($lblStatus, "正在创建封装程序...")
    
    ; 创建封装后的AutoIt脚本
    $sPackagedScript = CreatePackagedScript($sTargetPath, $sLsrunasePath)
    
    ; 保存临时脚本文件
    $sTempScript = @TempDir & "\PackagedApp_" & @YEAR & @MON & @MDAY & @HOUR & @MIN & @SEC & ".au3"
    $hFile = FileOpen($sTempScript, $FO_OVERWRITE)
    If $hFile = -1 Then
        MsgBox($MB_ICONERROR, "错误", "无法创建临时脚本文件！")
        Return
    EndIf
    FileWrite($hFile, $sPackagedScript)
    FileClose($hFile)
    
    GUICtrlSetData($lblStatus, "脚本已生成: " & $sTempScript & @CRLF & "请使用AutoIt编译器将其编译为EXE文件")
    
    ; 询问是否打开脚本文件
    $iChoice = MsgBox($MB_YESNO + $MB_ICONQUESTION, "完成", "封装脚本已生成！" & @CRLF & @CRLF & "脚本位置: " & $sTempScript & @CRLF & @CRLF & "是否打开脚本文件查看？")
    If $iChoice = $IDYES Then
        ShellExecute($sTempScript)
    EndIf
EndFunc

; 创建封装后的脚本内容
Func CreatePackagedScript($sTargetPath, $sLsrunasePath)
    ; 读取目标文件和lsrunase文件的二进制数据
    $hTargetFile = FileOpen($sTargetPath, $FO_BINARY)
    $bTargetData = FileRead($hTargetFile)
    FileClose($hTargetFile)
    
    $hLsrunaseFile = FileOpen($sLsrunasePath, $FO_BINARY)
    $bLsrunaseData = FileRead($hLsrunaseFile)
    FileClose($hLsrunaseFile)
    
    ; 获取目标文件名
    $sTargetFileName = StringRight($sTargetPath, StringLen($sTargetPath) - StringInStr($sTargetPath, "\", 0, -1))
    
    ; 创建脚本内容
    $sScript = '#include <FileConstants.au3>' & @CRLF
    $sScript &= '#include <MsgBoxConstants.au3>' & @CRLF & @CRLF
    
    $sScript &= '; 嵌入的文件数据' & @CRLF
    $sScript &= 'Global $bTargetData = "' & $bTargetData & '"' & @CRLF
    $sScript &= 'Global $bLsrunaseData = "' & $bLsrunaseData & '"' & @CRLF
    $sScript &= 'Global $sTargetFileName = "' & $sTargetFileName & '"' & @CRLF & @CRLF
    
    $sScript &= '; 主程序' & @CRLF
    $sScript &= 'Main()' & @CRLF & @CRLF
    
    $sScript &= 'Func Main()' & @CRLF
    $sScript &= '    ; 创建临时目录' & @CRLF
    $sScript &= '    $sTempDir = @TempDir & "\AdminRunner_" & @YEAR & @MON & @MDAY & @HOUR & @MIN & @SEC' & @CRLF
    $sScript &= '    DirCreate($sTempDir)' & @CRLF & @CRLF
    
    $sScript &= '    ; 释放文件' & @CRLF
    $sScript &= '    $sTargetPath = $sTempDir & "\" & $sTargetFileName' & @CRLF
    $sScript &= '    $sLsrunasePath = $sTempDir & "\lsrunase.exe"' & @CRLF & @CRLF
    
    $sScript &= '    ; 写入目标文件' & @CRLF
    $sScript &= '    $hTargetFile = FileOpen($sTargetPath, $FO_OVERWRITE + $FO_BINARY)' & @CRLF
    $sScript &= '    FileWrite($hTargetFile, $bTargetData)' & @CRLF
    $sScript &= '    FileClose($hTargetFile)' & @CRLF & @CRLF
    
    $sScript &= '    ; 写入lsrunase文件' & @CRLF
    $sScript &= '    $hLsrunaseFile = FileOpen($sLsrunasePath, $FO_OVERWRITE + $FO_BINARY)' & @CRLF
    $sScript &= '    FileWrite($hLsrunaseFile, $bLsrunaseData)' & @CRLF
    $sScript &= '    FileClose($hLsrunaseFile)' & @CRLF & @CRLF
    
    $sScript &= '    ; 获取目标程序所在目录' & @CRLF
    $sScript &= '    $sRunPath = StringLeft($sTargetPath, StringInStr($sTargetPath, "\", 0, -1))' & @CRLF & @CRLF
    
    $sScript &= '    ; 构建命令参数' & @CRLF
    $sScript &= '    $sCmdParams = ''/user:administrator /password:40lkg/kN/6Pm3ffhFw== /domain: /command:"'' & $sTargetPath & ''" /runpath:"'' & $sRunPath & ''"''' & @CRLF & @CRLF
    
    $sScript &= '    ; 执行命令' & @CRLF
    $sScript &= '    $iPID = Run(''"'' & $sLsrunasePath & ''" '' & $sCmdParams, $sTempDir, @SW_HIDE)' & @CRLF & @CRLF
    
    $sScript &= '    If $iPID > 0 Then' & @CRLF
    $sScript &= '        ; 等待一段时间确保程序启动' & @CRLF
    $sScript &= '        Sleep(2000)' & @CRLF
    $sScript &= '    Else' & @CRLF
    $sScript &= '        MsgBox($MB_ICONERROR, "错误", "启动程序失败！")' & @CRLF
    $sScript &= '    EndIf' & @CRLF & @CRLF
    
    $sScript &= '    ; 清理临时文件（可选）' & @CRLF
    $sScript &= '    ; FileDelete($sTargetPath)' & @CRLF
    $sScript &= '    ; FileDelete($sLsrunasePath)' & @CRLF
    $sScript &= '    ; DirRemove($sTempDir)' & @CRLF
    $sScript &= 'EndFunc' & @CRLF
    
    Return $sScript
EndFunc
