#include <ButtonConstants.au3>
#include <EditConstants.au3>
#include <GUIConstantsEx.au3>
#include <StaticConstants.au3>
#include <WindowsConstants.au3>
#include <FileConstants.au3>
#include <MsgBoxConstants.au3>

; 创建主窗体
$hGUI = GUICreate("EXE封装工具 - 简化版", 450, 250, -1, -1)

; 创建控件
$lblTitle = GUICtrlCreateLabel("将程序封装为管理员权限运行的单一EXE", 20, 15, 410, 20, $SS_CENTER)
GUICtrlSetFont($lblTitle, 11, 600)

; 目标程序选择
$lblTarget = GUICtrlCreateLabel("选择要封装的程序:", 20, 50, 120, 20)
$txtTargetPath = GUICtrlCreateInput("", 20, 70, 320, 22, $ES_READONLY)
$btnBrowse = GUICtrlCreateButton("浏览", 350, 69, 70, 24)

; 输出文件名
$lblOutput = GUICtrlCreateLabel("输出文件名:", 20, 110, 80, 20)
$txtOutputName = GUICtrlCreateInput("", 20, 130, 320, 22)
$lblTip = GUICtrlCreateLabel("(不需要.exe扩展名)", 350, 132, 100, 20)
GUICtrlSetColor($lblTip, 0x808080)

; 操作按钮
$btnCreate = GUICtrlCreateButton("生成封装脚本", 120, 170, 100, 35)
GUICtrlSetFont($btnCreate, 10, 600)
$btnExit = GUICtrlCreateButton("退出", 240, 170, 70, 35)

; 状态信息
$lblStatus = GUICtrlCreateLabel("请选择要封装的程序文件", 20, 220, 410, 20)
GUICtrlSetColor($lblStatus, 0x0000FF)

; 显示窗体
GUISetState(@SW_SHOW, $hGUI)

; 主事件循环
While 1
    $nMsg = GUIGetMsg()
    Switch $nMsg
        Case $GUI_EVENT_CLOSE, $btnExit
            Exit
            
        Case $btnBrowse
            $sFile = FileOpenDialog("选择要封装的程序", @ProgramFilesDir, "可执行文件 (*.exe)", $FD_FILEMUSTEXIST)
            If Not @error Then
                GUICtrlSetData($txtTargetPath, $sFile)
                ; 自动生成输出文件名
                $sBaseName = StringTrimRight(StringRight($sFile, StringLen($sFile) - StringInStr($sFile, "\", 0, -1)), 4)
                GUICtrlSetData($txtOutputName, $sBaseName & "_Admin")
                GUICtrlSetData($lblStatus, "已选择: " & StringRight($sFile, 40))
            EndIf
            
        Case $btnCreate
            CreatePackageScript()
    EndSwitch
WEnd

; 生成封装脚本
Func CreatePackageScript()
    $sTargetPath = GUICtrlRead($txtTargetPath)
    $sOutputName = GUICtrlRead($txtOutputName)
    
    ; 验证输入
    If $sTargetPath = "" Then
        MsgBox(48, "提示", "请先选择要封装的程序！")
        Return
    EndIf
    
    If $sOutputName = "" Then
        MsgBox(48, "提示", "请输入输出文件名！")
        Return
    EndIf
    
    If Not FileExists($sTargetPath) Then
        MsgBox(16, "错误", "目标程序不存在！")
        Return
    EndIf
    
    $sLsrunasePath = @ScriptDir & "\提权程序\lsrunase.exe"
    If Not FileExists($sLsrunasePath) Then
        MsgBox(16, "错误", "找不到lsrunase.exe！")
        Return
    EndIf
    
    GUICtrlSetData($lblStatus, "正在生成封装脚本...")
    
    ; 读取文件为Base64
    $sTargetBase64 = FileToBase64($sTargetPath)
    $sLsrunaseBase64 = FileToBase64($sLsrunasePath)
    $sTargetFileName = StringRight($sTargetPath, StringLen($sTargetPath) - StringInStr($sTargetPath, "\", 0, -1))
    
    ; 生成脚本内容
    $sScript = GenerateScript($sTargetBase64, $sLsrunaseBase64, $sTargetFileName)
    
    ; 保存脚本
    $sScriptPath = @DesktopDir & "\" & $sOutputName & ".au3"
    $hFile = FileOpen($sScriptPath, 2)
    FileWrite($hFile, $sScript)
    FileClose($hFile)
    
    GUICtrlSetData($lblStatus, "脚本已生成到桌面")
    
    MsgBox(64, "完成", "封装脚本已生成！" & @CRLF & @CRLF & "位置: " & $sScriptPath & @CRLF & @CRLF & "请使用AutoIt编译器编译为EXE文件")
EndFunc

; 文件转Base64
Func FileToBase64($sFilePath)
    $hFile = FileOpen($sFilePath, 16) ; 二进制模式
    $bData = FileRead($hFile)
    FileClose($hFile)
    Return _Base64Encode($bData)
EndFunc

; Base64编码函数
Func _Base64Encode($bData)
    $aCall = DllCall("crypt32.dll", "bool", "CryptBinaryToStringW", "ptr", $bData, "dword", BinaryLen($bData), "dword", 1, "ptr", 0, "dword*", 0)
    If @error Or Not $aCall[0] Then Return SetError(1, 0, "")
    
    $sBase64 = DllStructCreate("wchar[" & $aCall[5] & "]")
    $aCall = DllCall("crypt32.dll", "bool", "CryptBinaryToStringW", "ptr", $bData, "dword", BinaryLen($bData), "dword", 1, "ptr", DllStructGetPtr($sBase64), "dword*", $aCall[5])
    If @error Or Not $aCall[0] Then Return SetError(2, 0, "")
    
    Return StringReplace(DllStructGetData($sBase64, 1), @CRLF, "")
EndFunc

; 生成封装脚本内容
Func GenerateScript($sTargetBase64, $sLsrunaseBase64, $sTargetFileName)
    $sScript = '; 自动生成的封装程序 - 以管理员权限运行' & @CRLF
    $sScript &= '; 目标程序: ' & $sTargetFileName & @CRLF
    $sScript &= '; 生成时间: ' & @YEAR & '-' & @MON & '-' & @MDAY & ' ' & @HOUR & ':' & @MIN & @CRLF & @CRLF
    
    $sScript &= '#include <FileConstants.au3>' & @CRLF
    $sScript &= '#include <MsgBoxConstants.au3>' & @CRLF & @CRLF
    
    $sScript &= '; 嵌入的文件数据 (Base64编码)' & @CRLF
    $sScript &= 'Global $sTargetData = "' & $sTargetBase64 & '"' & @CRLF
    $sScript &= 'Global $sLsrunaseData = "' & $sLsrunaseBase64 & '"' & @CRLF
    $sScript &= 'Global $sTargetFileName = "' & $sTargetFileName & '"' & @CRLF & @CRLF
    
    $sScript &= '; 主程序入口' & @CRLF
    $sScript &= 'Main()' & @CRLF & @CRLF
    
    $sScript &= 'Func Main()' & @CRLF
    $sScript &= '    ; 创建临时目录' & @CRLF
    $sScript &= '    $sTempDir = @TempDir & "\AR_" & Random(1000, 9999, 1)' & @CRLF
    $sScript &= '    DirCreate($sTempDir)' & @CRLF & @CRLF
    
    $sScript &= '    ; 释放文件到临时目录' & @CRLF
    $sScript &= '    $sTargetPath = $sTempDir & "\" & $sTargetFileName' & @CRLF
    $sScript &= '    $sLsrunasePath = $sTempDir & "\lsrunase.exe"' & @CRLF & @CRLF
    
    $sScript &= '    ; 解码并写入目标文件' & @CRLF
    $sScript &= '    $bTargetBinary = _Base64Decode($sTargetData)' & @CRLF
    $sScript &= '    FileWrite($sTargetPath, $bTargetBinary)' & @CRLF & @CRLF
    
    $sScript &= '    ; 解码并写入lsrunase文件' & @CRLF
    $sScript &= '    $bLsrunaseBinary = _Base64Decode($sLsrunaseData)' & @CRLF
    $sScript &= '    FileWrite($sLsrunasePath, $bLsrunaseBinary)' & @CRLF & @CRLF
    
    $sScript &= '    ; 构建运行命令' & @CRLF
    $sScript &= '    $sCmdLine = ''/user:administrator /password:40lkg/kN/6Pm3ffhFw== /domain: /command:"'' & $sTargetPath & ''" /runpath:"'' & $sTempDir & ''"''' & @CRLF & @CRLF
    
    $sScript &= '    ; 以管理员权限运行目标程序' & @CRLF
    $sScript &= '    Run(''"'' & $sLsrunasePath & ''" '' & $sCmdLine, $sTempDir, @SW_HIDE)' & @CRLF & @CRLF
    
    $sScript &= '    ; 等待程序启动后清理临时文件' & @CRLF
    $sScript &= '    Sleep(3000)' & @CRLF
    $sScript &= '    FileDelete($sTargetPath)' & @CRLF
    $sScript &= '    FileDelete($sLsrunasePath)' & @CRLF
    $sScript &= '    DirRemove($sTempDir)' & @CRLF
    $sScript &= 'EndFunc' & @CRLF & @CRLF
    
    $sScript &= '; Base64解码函数' & @CRLF
    $sScript &= 'Func _Base64Decode($sData)' & @CRLF
    $sScript &= '    $aCall = DllCall("crypt32.dll", "bool", "CryptStringToBinaryW", "wstr", $sData, "dword", 0, "dword", 1, "ptr", 0, "dword*", 0, "ptr", 0, "ptr", 0)' & @CRLF
    $sScript &= '    If @error Or Not $aCall[0] Then Return SetError(1, 0, "")' & @CRLF
    $sScript &= '    $bBuffer = DllStructCreate("byte[" & $aCall[5] & "]")' & @CRLF
    $sScript &= '    $aCall = DllCall("crypt32.dll", "bool", "CryptStringToBinaryW", "wstr", $sData, "dword", 0, "dword", 1, "ptr", DllStructGetPtr($bBuffer), "dword*", $aCall[5], "ptr", 0, "ptr", 0)' & @CRLF
    $sScript &= '    Return DllStructGetData($bBuffer, 1)' & @CRLF
    $sScript &= 'EndFunc'
    
    Return $sScript
EndFunc
